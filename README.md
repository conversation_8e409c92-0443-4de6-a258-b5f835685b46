# Stop Motion Creator

Transform your videos into stunning stop-motion animations with ease! 🎬

## Features

- 🎥 **Easy Upload**: Drag-and-drop video upload interface
- ⚡ **Fast Processing**: Quick frame extraction and rendering using FFmpeg
- 🎨 **Visual Effects**: Apply cartoon, vibrant, clay, retro, and sketch filters
- ⚙️ **Custom Settings**: Control frame interval and playback speed
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 💾 **Multiple Export Formats**: Export as MP4, GIF, or WEBM
- 🔗 **Social Sharing**: Share directly to Instagram, TikTok, Twitter, and Facebook

## Tech Stack

### Frontend
- React 18 with Error Boundaries
- Socket.io Client (for real-time progress updates)
- React Dropzone (for file uploads)
- Axios (for API requests)
- PropTypes (for type validation)

### Backend
- Node.js with Express
- FFmpeg (for video processing)
- Multer (for file uploads)
- Socket.io (for real-time communication)
- Helmet (for security headers)
- Rate Limiting (for API protection)
- Jo<PERSON> (for input validation)
- System monitoring and resource management

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v14 or higher)
- npm or yarn
- FFmpeg (must be accessible from command line)

### Installing FFmpeg

#### Windows
1. Download FFmpeg from https://ffmpeg.org/download.html
2. Extract the files to a folder (e.g., `C:\ffmpeg`)
3. Add FFmpeg to your PATH environment variable
4. Verify installation: `ffmpeg -version`

#### macOS
```bash
brew install ffmpeg
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
```

## Security & Reliability Features

### Security Enhancements
- **Input Validation**: Comprehensive validation using Joi schemas
- **Path Traversal Protection**: Secure file handling with path validation
- **Rate Limiting**: API protection against abuse (100 requests per 15 minutes)
- **Security Headers**: Helmet.js for security headers
- **File Type Validation**: Both extension and MIME type checking
- **CORS Configuration**: Environment-based CORS setup

### Reliability Features
- **Error Boundaries**: React error boundaries for graceful error handling
- **Resource Monitoring**: System memory and disk space monitoring
- **Concurrent Job Limiting**: Prevents server overload (max 3 concurrent jobs)
- **Graceful Shutdown**: Proper cleanup on server shutdown
- **Health Check Endpoint**: `/api/health` for monitoring server status
- **Automatic Cleanup**: Old files cleaned up every 30 minutes
- **Timeout Protection**: Processing timeouts to prevent hanging operations

### Error Handling
- **Comprehensive Logging**: Structured logging throughout the application
- **Socket Error Handling**: Robust WebSocket error management
- **FFmpeg Validation**: Startup checks for FFmpeg availability
- **Network Error Recovery**: Automatic reconnection and error recovery

## Installation

1. Clone or navigate to the project directory:
```bash
cd stop-motion
```

2. Run the setup script (recommended):

**Windows:**
```bash
setup.bat
```

**macOS/Linux:**
```bash
chmod +x setup.sh
./setup.sh
```

**Or install manually:**

3. Install backend dependencies:
```bash
cd backend
npm install
```

4. Install frontend dependencies:
```bash
cd ../frontend
npm install
```

5. Set up environment files:
```bash
# Copy example environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

## Running the Application

### Development Mode

1. Start the backend server:
```bash
cd backend
npm start
```
The server will run on http://localhost:5000

2. In a new terminal, start the frontend:
```bash
cd frontend
npm start
```
The app will open at http://localhost:3000

### Production Build

1. Build the frontend:
```bash
cd frontend
npm run build
```

2. Serve the built files with your backend or deploy to a hosting service.

## Usage

1. **Upload Video**: Drag and drop or click to upload a video file (MP4, AVI, MOV, MKV, WEBM)
2. **Adjust Settings**:
   - Frame Interval: Set how many seconds between extracted frames
   - Output Speed: Set the frames per second for the output video
   - Visual Filter: Choose from various artistic effects
3. **Process**: Click "Create Stop Motion" to generate your video
4. **Preview**: View the original and stop-motion versions
5. **Export**: Download the result or share on social media

## API Endpoints

- `GET /api/health` - Health check and system status
- `POST /api/upload` - Upload a video file (with validation and security checks)
- `POST /api/process` - Process video with stop-motion effect (with resource monitoring)
- `GET /api/download/:filename` - Download processed video (with path traversal protection)
- `GET /output/:filename` - Stream video file (with security headers)

## Project Structure

```
stop-motion/
├── backend/
│   ├── uploads/        # Temporary uploaded files
│   ├── output/         # Processed videos
│   ├── server.js       # Express server
│   └── package.json
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── UploadSection.js
│   │   │   ├── SettingsPanel.js
│   │   │   ├── VideoPreview.js
│   │   │   ├── ProgressBar.js
│   │   │   └── ExportSection.js
│   │   ├── App.js
│   │   ├── App.css
│   │   └── index.js
│   └── package.json
└── README.md
```

## Configuration

### Environment Variables

#### Backend (.env)
```bash
# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_DIR=uploads
OUTPUT_DIR=output

# Processing Configuration
MAX_CONCURRENT_JOBS=3
CLEANUP_INTERVAL_MINUTES=30
FILE_MAX_AGE_HOURS=1

# Rate Limiting (requests per 15 minutes)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# FFmpeg Configuration
FFMPEG_PATH=ffmpeg
```

#### Frontend (.env)
```bash
# API Configuration
REACT_APP_API_URL=http://localhost:5000

# App Configuration
REACT_APP_MAX_FILE_SIZE=104857600
REACT_APP_SUPPORTED_FORMATS=mp4,avi,mov,mkv,webm
```

## Troubleshooting

### FFmpeg not found
- Ensure FFmpeg is installed and added to PATH
- Restart your terminal after installation
- Test with `ffmpeg -version`

### Upload fails
- Check file size (max 100MB)
- Ensure file format is supported
- Check server logs for errors

### Processing hangs
- Large videos may take time to process
- Check available disk space
- Monitor server console for progress

## Future Enhancements

- [ ] User authentication and project saving
- [ ] Batch processing for multiple videos
- [ ] Advanced frame-by-frame editor
- [ ] Custom watermarks
- [ ] Cloud storage integration
- [ ] Direct social media API integration
- [ ] More visual effects and filters
- [ ] Video trimming before processing

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Support

For issues or questions, please create an issue in the repository.

---

Made with ❤️ by Stop Motion Creator Team
