# Stop Motion Creator

Transform your videos into stunning stop-motion animations with ease! 🎬

## Features

- 🎥 **Easy Upload**: Drag-and-drop video upload interface
- ⚡ **Fast Processing**: Quick frame extraction and rendering using FFmpeg
- 🎨 **Visual Effects**: Apply cartoon, vibrant, clay, retro, and sketch filters
- ⚙️ **Custom Settings**: Control frame interval and playback speed
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 💾 **Multiple Export Formats**: Export as MP4, GIF, or WEBM
- 🔗 **Social Sharing**: Share directly to Instagram, TikTok, Twitter, and Facebook

## Tech Stack

### Frontend
- React 18
- Socket.io Client (for real-time progress updates)
- React Dropzone (for file uploads)
- Axios (for API requests)

### Backend
- Node.js with Express
- FFmpeg (for video processing)
- Multer (for file uploads)
- Socket.io (for real-time communication)

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v14 or higher)
- npm or yarn
- FFmpeg (must be accessible from command line)

### Installing FFmpeg

#### Windows
1. Download FFmpeg from https://ffmpeg.org/download.html
2. Extract the files to a folder (e.g., `C:\ffmpeg`)
3. Add FFmpeg to your PATH environment variable
4. Verify installation: `ffmpeg -version`

#### macOS
```bash
brew install ffmpeg
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
```

## Installation

1. Clone or navigate to the project directory:
```bash
cd stop-motion
```

2. Install backend dependencies:
```bash
cd backend
npm install
```

3. Install frontend dependencies:
```bash
cd ../frontend
npm install
```

## Running the Application

### Development Mode

1. Start the backend server:
```bash
cd backend
npm start
```
The server will run on http://localhost:5000

2. In a new terminal, start the frontend:
```bash
cd frontend
npm start
```
The app will open at http://localhost:3000

### Production Build

1. Build the frontend:
```bash
cd frontend
npm run build
```

2. Serve the built files with your backend or deploy to a hosting service.

## Usage

1. **Upload Video**: Drag and drop or click to upload a video file (MP4, AVI, MOV, MKV, WEBM)
2. **Adjust Settings**:
   - Frame Interval: Set how many seconds between extracted frames
   - Output Speed: Set the frames per second for the output video
   - Visual Filter: Choose from various artistic effects
3. **Process**: Click "Create Stop Motion" to generate your video
4. **Preview**: View the original and stop-motion versions
5. **Export**: Download the result or share on social media

## API Endpoints

- `POST /api/upload` - Upload a video file
- `POST /api/process` - Process video with stop-motion effect
- `GET /api/download/:filename` - Download processed video
- `GET /output/:filename` - Stream video file

## Project Structure

```
stop-motion/
├── backend/
│   ├── uploads/        # Temporary uploaded files
│   ├── output/         # Processed videos
│   ├── server.js       # Express server
│   └── package.json
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── UploadSection.js
│   │   │   ├── SettingsPanel.js
│   │   │   ├── VideoPreview.js
│   │   │   ├── ProgressBar.js
│   │   │   └── ExportSection.js
│   │   ├── App.js
│   │   ├── App.css
│   │   └── index.js
│   └── package.json
└── README.md
```

## Configuration

### Backend Configuration
- Port: Default 5000 (configurable via `PORT` environment variable)
- File size limit: 100MB
- Automatic cleanup: Files older than 1 hour are deleted

### Frontend Configuration
- API URL: Default http://localhost:5000 (configurable via `REACT_APP_API_URL`)

## Troubleshooting

### FFmpeg not found
- Ensure FFmpeg is installed and added to PATH
- Restart your terminal after installation
- Test with `ffmpeg -version`

### Upload fails
- Check file size (max 100MB)
- Ensure file format is supported
- Check server logs for errors

### Processing hangs
- Large videos may take time to process
- Check available disk space
- Monitor server console for progress

## Future Enhancements

- [ ] User authentication and project saving
- [ ] Batch processing for multiple videos
- [ ] Advanced frame-by-frame editor
- [ ] Custom watermarks
- [ ] Cloud storage integration
- [ ] Direct social media API integration
- [ ] More visual effects and filters
- [ ] Video trimming before processing

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Support

For issues or questions, please create an issue in the repository.

---

Made with ❤️ by Stop Motion Creator Team
