const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const ffmpeg = require('fluent-ffmpeg');
const { Server } = require('socket.io');
const http = require('http');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/output', express.static(path.join(__dirname, 'output')));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    await fs.mkdir(uploadDir, { recursive: true });
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /mp4|avi|mov|mkv|webm/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only video files are allowed'));
    }
  }
});

// Socket.io connection for progress updates
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Helper function to get video metadata
const getVideoMetadata = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) reject(err);
      else resolve(metadata);
    });
  });
};

// Helper function to extract frames
const extractFrames = async (videoPath, outputDir, interval, socketId) => {
  await fs.mkdir(outputDir, { recursive: true });
  
  return new Promise((resolve, reject) => {
    console.log(`Extracting frames from: ${videoPath}`);
    console.log(`Output directory: ${outputDir}`);
    console.log(`Interval: ${interval}`);
    
    const framePattern = path.join(outputDir, 'frame_%04d.png');
    
    ffmpeg(videoPath)
      .on('start', (commandLine) => {
        console.log('FFmpeg command:', commandLine);
      })
      .on('progress', (progress) => {
        console.log('Processing: ' + (progress.percent || 0) + '% done');
        if (socketId) {
          io.to(socketId).emit('progress', {
            stage: 'extracting',
            percent: progress.percent || 0
          });
        }
      })
      .on('error', (err) => {
        console.error('FFmpeg error:', err.message);
        reject(err);
      })
      .on('end', async () => {
        console.log('Frame extraction finished');
        try {
          const files = await fs.readdir(outputDir);
          const frameFiles = files.filter(f => f.startsWith('frame_'))
            .sort()
            .map(f => path.join(outputDir, f));
          console.log(`Extracted ${frameFiles.length} frames`);
          resolve(frameFiles);
        } catch (err) {
          console.error('Error reading frame files:', err);
          reject(err);
        }
      })
      .outputOptions([
        `-vf fps=1/${interval}`
      ])
      .output(framePattern)
      .run();
  });
};

// Helper function to apply filters
const getFilterString = (filter) => {
  switch(filter) {
    case 'cartoon':
      return 'edgedetect=low=0.1:high=0.4,negate';
    case 'vibrant':
      return 'eq=saturation=1.5:brightness=0.1';
    case 'clay':
      return 'colorlevels=rimax=0.902:gimax=0.902:bimax=0.902,unsharp=5:5:1.0:5:5:0.0';
    case 'retro':
      return 'curves=vintage,vibrance=intensity=0.5';
    case 'sketch':
      return 'edgedetect=mode=colormix:high=0.1:low=0.1';
    default:
      return null;
  }
};

// Helper function to create stop motion video
const createStopMotion = async (frames, outputPath, fps, filter, socketId) => {
  return new Promise(async (resolve, reject) => {
    console.log(`Creating stop motion with ${frames.length} frames`);
    console.log(`Output path: ${outputPath}`);
    console.log(`FPS: ${fps}, Filter: ${filter}`);
    
    if (frames.length === 0) {
      reject(new Error('No frames to process'));
      return;
    }
    
    // Create a concat file for FFmpeg
    const tempDir = path.dirname(frames[0]);
    const concatFile = path.join(tempDir, 'concat.txt');
    const concatContent = frames.map(f => `file '${f.replace(/\\/g, '/')}'`).join('\n');
    
    try {
      await fs.writeFile(concatFile, concatContent);
      console.log('Created concat file with content:', concatContent.substring(0, 200));
      
      let ffmpegCommand = ffmpeg()
        .input(concatFile)
        .inputOptions(['-f', 'concat', '-safe', '0']);
      
      // Apply filter if selected
      if (filter && filter !== 'none') {
        const filterString = getFilterString(filter);
        if (filterString) {
          ffmpegCommand = ffmpegCommand.videoFilters(filterString);
        }
      }
      
      ffmpegCommand
        .outputOptions([
          '-r', fps.toString(),
          '-c:v', 'libx264',
          '-pix_fmt', 'yuv420p',
          '-preset', 'fast'
        ])
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('FFmpeg command:', commandLine);
        })
        .on('progress', (progress) => {
          console.log('Creating video: ' + (progress.percent || 0) + '% done');
          if (socketId) {
            io.to(socketId).emit('progress', {
              stage: 'creating',
              percent: progress.percent || 0
            });
          }
        })
        .on('end', async () => {
          console.log('Stop motion video created successfully');
          // Clean up concat file
          try {
            await fs.unlink(concatFile);
          } catch (err) {
            console.error('Error deleting concat file:', err);
          }
          resolve(outputPath);
        })
        .on('error', (err) => {
          console.error('FFmpeg error during video creation:', err.message);
          // Clean up concat file on error
          fs.unlink(concatFile).catch(console.error);
          reject(err);
        })
        .run();
    } catch (err) {
      console.error('Error creating concat file:', err);
      reject(err);
    }
  });
};

// Routes
app.post('/api/upload', upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No video file uploaded' });
    }
    
    const metadata = await getVideoMetadata(req.file.path);
    
    res.json({
      success: true,
      file: {
        id: path.parse(req.file.filename).name,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        duration: metadata.format.duration,
        width: metadata.streams[0].width,
        height: metadata.streams[0].height
      }
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/process', async (req, res) => {
  try {
    const { fileId, interval, fps, filter, socketId } = req.body;
    
    if (!fileId) {
      return res.status(400).json({ error: 'No file ID provided' });
    }
    
    // Set defaults
    const frameInterval = interval || 0.3;
    const outputFps = fps || 10;
    const selectedFilter = filter || 'none';
    
    // Find the uploaded file
    let videoPath = null;
    const extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
    
    for (const ext of extensions) {
      const testPath = path.join(__dirname, 'uploads', `${fileId}${ext}`);
      try {
        await fs.access(testPath);
        videoPath = testPath;
        console.log(`Found video file: ${videoPath}`);
        break;
      } catch {
        // Continue checking other extensions
      }
    }
    
    if (!videoPath) {
      console.error(`Video file not found for ID: ${fileId}`);
      return res.status(404).json({ error: 'Video file not found' });
    }
    
    const framesDir = path.join(__dirname, 'uploads', `${fileId}_frames`);
    const outputFilename = `${fileId}_stopmotion.mp4`;
    const outputPath = path.join(__dirname, 'output', outputFilename);
    
    // Extract frames
    console.log('Starting frame extraction...');
    const frames = await extractFrames(videoPath, framesDir, frameInterval, socketId);
    
    if (frames.length === 0) {
      throw new Error('No frames extracted from video');
    }
    
    console.log(`Successfully extracted ${frames.length} frames`);
    
    // Create stop motion video
    console.log('Creating stop motion video...');
    await createStopMotion(frames, outputPath, outputFps, selectedFilter, socketId);
    
    // Clean up frames directory
    try {
      await fs.rm(framesDir, { recursive: true, force: true });
      console.log('Cleaned up temporary frames');
    } catch (err) {
      console.error('Error cleaning up frames:', err);
    }
    
    // Send completion
    if (socketId) {
      io.to(socketId).emit('progress', {
        stage: 'complete',
        percent: 100
      });
    }
    
    res.json({
      success: true,
      output: {
        filename: outputFilename,
        url: `/output/${outputFilename}`,
        frameCount: frames.length
      }
    });
    
  } catch (error) {
    console.error('Processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/download/:filename', async (req, res) => {
  try {
    const filepath = path.join(__dirname, 'output', req.params.filename);
    await fs.access(filepath);
    res.download(filepath);
  } catch (error) {
    res.status(404).json({ error: 'File not found' });
  }
});

// Cleanup old files periodically
setInterval(async () => {
  try {
    const uploadsDir = path.join(__dirname, 'uploads');
    const outputDir = path.join(__dirname, 'output');
    const maxAge = 60 * 60 * 1000; // 1 hour
    
    const cleanOldFiles = async (dir) => {
      const files = await fs.readdir(dir);
      for (const file of files) {
        const filepath = path.join(dir, file);
        const stat = await fs.stat(filepath);
        if (Date.now() - stat.mtime > maxAge) {
          await fs.unlink(filepath).catch(console.error);
        }
      }
    };
    
    await cleanOldFiles(uploadsDir);
    await cleanOldFiles(outputDir);
  } catch (error) {
    console.error('Cleanup error:', error);
  }
}, 30 * 60 * 1000); // Run every 30 minutes

server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
