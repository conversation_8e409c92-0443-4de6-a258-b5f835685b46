require('dotenv').config();
const express = require('express');
const multer = require('multer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');
const ffmpeg = require('fluent-ffmpeg');
const { Server } = require('socket.io');
const http = require('http');
const SystemMonitor = require('./utils/systemMonitor');

// Configuration
const config = {
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 100 * 1024 * 1024,
  uploadDir: process.env.UPLOAD_DIR || 'uploads',
  outputDir: process.env.OUTPUT_DIR || 'output',
  maxConcurrentJobs: parseInt(process.env.MAX_CONCURRENT_JOBS) || 3,
  cleanupIntervalMinutes: parseInt(process.env.CLEANUP_INTERVAL_MINUTES) || 30,
  fileMaxAgeHours: parseInt(process.env.FILE_MAX_AGE_HOURS) || 1,
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  ffmpegPath: process.env.FFMPEG_PATH || 'ffmpeg'
};

// Validation schemas
const uploadSchema = Joi.object({
  interval: Joi.number().min(0.1).max(5).default(0.3),
  fps: Joi.number().min(1).max(60).default(10),
  filter: Joi.string().valid('none', 'cartoon', 'vibrant', 'clay', 'retro', 'sketch').default('none'),
  socketId: Joi.string().optional()
});

// Utility functions
const logger = {
  info: (message, data = {}) => console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data),
  error: (message, error = {}) => console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error),
  warn: (message, data = {}) => console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, data)
};

// Global state for tracking concurrent jobs
let activeJobs = 0;

// Initialize system monitor
const systemMonitor = new SystemMonitor(logger);

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: config.frontendUrl,
    methods: ["GET", "POST"]
  }
});

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false // Allow video embedding
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimitWindowMs,
  max: config.rateLimitMaxRequests,
  message: { error: 'Too many requests, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to API routes only
app.use('/api', limiter);

// CORS configuration
app.use(cors({
  origin: config.frontendUrl,
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving with security
app.use('/output', express.static(path.join(__dirname, config.outputDir), {
  setHeaders: (res, filePath) => {
    // Only allow video files to be served
    const ext = path.extname(filePath).toLowerCase();
    if (['.mp4', '.webm', '.avi', '.mov', '.mkv'].includes(ext)) {
      res.setHeader('Content-Type', `video/${ext.slice(1)}`);
    }
  }
}));

// Check if FFmpeg is available
const checkFFmpegAvailability = () => {
  return new Promise((resolve) => {
    ffmpeg.getAvailableFormats((err) => {
      if (err) {
        logger.error('FFmpeg not available', err);
        resolve(false);
      } else {
        logger.info('FFmpeg is available');
        resolve(true);
      }
    });
  });
};

// Validate filename for security
const isValidFilename = (filename) => {
  // Prevent path traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return false;
  }

  // Allow common filename characters including spaces, but prevent dangerous ones
  const dangerousPattern = /[<>:"|?*\x00-\x1f]/;
  return !dangerousPattern.test(filename) && filename.length > 0 && filename.length < 255;
};

// Check available disk space
const checkDiskSpace = async (directory) => {
  try {
    const stats = await fs.stat(directory);
    // This is a basic check - in production you'd want a more sophisticated disk space check
    return true;
  } catch (error) {
    logger.error('Error checking disk space', error);
    return false;
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, config.uploadDir);
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      const hasSpace = await checkDiskSpace(uploadDir);
      if (!hasSpace) {
        return cb(new Error('Insufficient disk space'));
      }
      cb(null, uploadDir);
    } catch (error) {
      logger.error('Error creating upload directory', error);
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: config.maxFileSize,
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Check file extension
    const allowedExtensions = /\.(mp4|avi|mov|mkv|webm)$/i;
    const extname = allowedExtensions.test(file.originalname);

    // Check MIME type
    const allowedMimeTypes = [
      'video/mp4',
      'video/avi',
      'video/quicktime',
      'video/x-msvideo',
      'video/x-matroska',
      'video/webm'
    ];
    const mimetype = allowedMimeTypes.includes(file.mimetype);

    // Validate filename for security
    if (!isValidFilename(file.originalname)) {
      return cb(new Error('Invalid filename. Only alphanumeric characters, dots, hyphens, and underscores are allowed.'));
    }

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only video files (MP4, AVI, MOV, MKV, WEBM) are allowed'));
    }
  }
});

// Socket.io connection for progress updates
io.on('connection', (socket) => {
  logger.info('Client connected', { socketId: socket.id });

  socket.on('disconnect', (reason) => {
    logger.info('Client disconnected', { socketId: socket.id, reason });
  });

  socket.on('error', (error) => {
    logger.error('Socket error', { socketId: socket.id, error });
  });
});

// Helper function to get video metadata
const getVideoMetadata = (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) reject(err);
      else resolve(metadata);
    });
  });
};

// Helper function to extract frames
const extractFrames = async (videoPath, outputDir, interval, socketId) => {
  await fs.mkdir(outputDir, { recursive: true });

  return new Promise((resolve, reject) => {
    logger.info('Starting frame extraction', { videoPath, outputDir, interval });

    const framePattern = path.join(outputDir, 'frame_%04d.png');

    const command = ffmpeg(videoPath)
      .on('start', (commandLine) => {
        logger.info('FFmpeg command started', { command: commandLine });
      })
      .on('progress', (progress) => {
        const percent = Math.round(progress.percent || 0);
        logger.info('Frame extraction progress', { percent });
        if (socketId) {
          io.to(socketId).emit('progress', {
            stage: 'extracting',
            percent: percent
          });
        }
      })
      .on('error', (err) => {
        logger.error('FFmpeg frame extraction error', { error: err.message });
        reject(new Error(`Frame extraction failed: ${err.message}`));
      })
      .on('end', async () => {
        logger.info('Frame extraction finished');
        try {
          const files = await fs.readdir(outputDir);
          const frameFiles = files.filter(f => f.startsWith('frame_'))
            .sort()
            .map(f => path.join(outputDir, f));

          if (frameFiles.length === 0) {
            reject(new Error('No frames were extracted from the video'));
            return;
          }

          logger.info('Frame extraction completed successfully', { frameCount: frameFiles.length });
          resolve(frameFiles);
        } catch (err) {
          logger.error('Error reading frame files', { error: err.message });
          reject(new Error(`Failed to read extracted frames: ${err.message}`));
        }
      })
      .outputOptions([
        `-vf fps=1/${interval}`,
        '-y' // Overwrite output files
      ])
      .output(framePattern);

    // Set timeout for frame extraction (5 minutes)
    const timeout = setTimeout(() => {
      command.kill('SIGKILL');
      reject(new Error('Frame extraction timed out'));
    }, 5 * 60 * 1000);

    command.on('end', () => clearTimeout(timeout));
    command.on('error', () => clearTimeout(timeout));

    command.run();
  });
};

// Helper function to apply filters
const getFilterString = (filter) => {
  switch(filter) {
    case 'cartoon':
      return 'edgedetect=low=0.1:high=0.4,negate';
    case 'vibrant':
      return 'eq=saturation=1.5:brightness=0.1';
    case 'clay':
      return 'colorlevels=rimax=0.902:gimax=0.902:bimax=0.902,unsharp=5:5:1.0:5:5:0.0';
    case 'retro':
      return 'curves=vintage,vibrance=intensity=0.5';
    case 'sketch':
      return 'edgedetect=mode=colormix:high=0.1:low=0.1';
    default:
      return null;
  }
};

// Helper function to create stop motion video
const createStopMotion = async (frames, outputPath, fps, filter, socketId) => {
  return new Promise(async (resolve, reject) => {
    logger.info('Starting stop motion creation', {
      frameCount: frames.length,
      outputPath,
      fps,
      filter
    });

    if (frames.length === 0) {
      reject(new Error('No frames to process'));
      return;
    }

    // Create a concat file for FFmpeg
    const tempDir = path.dirname(frames[0]);
    const concatFile = path.join(tempDir, 'concat.txt');
    const concatContent = frames.map(f => `file '${f.replace(/\\/g, '/')}'`).join('\n');

    try {
      await fs.writeFile(concatFile, concatContent);
      logger.info('Created concat file', { frameCount: frames.length });

      let ffmpegCommand = ffmpeg()
        .input(concatFile)
        .inputOptions(['-f', 'concat', '-safe', '0']);

      // Apply filter if selected
      if (filter && filter !== 'none') {
        const filterString = getFilterString(filter);
        if (filterString) {
          ffmpegCommand = ffmpegCommand.videoFilters(filterString);
          logger.info('Applied video filter', { filter, filterString });
        }
      }

      const command = ffmpegCommand
        .outputOptions([
          '-r', fps.toString(),
          '-c:v', 'libx264',
          '-pix_fmt', 'yuv420p',
          '-preset', 'fast',
          '-y' // Overwrite output file
        ])
        .output(outputPath)
        .on('start', (commandLine) => {
          logger.info('FFmpeg video creation command started', { command: commandLine });
        })
        .on('progress', (progress) => {
          const percent = Math.round(progress.percent || 0);
          logger.info('Video creation progress', { percent });
          if (socketId) {
            io.to(socketId).emit('progress', {
              stage: 'creating',
              percent: percent
            });
          }
        })
        .on('end', async () => {
          logger.info('Stop motion video created successfully');
          // Clean up concat file
          try {
            await fs.unlink(concatFile);
          } catch (err) {
            logger.warn('Error deleting concat file', { error: err.message });
          }
          resolve(outputPath);
        })
        .on('error', (err) => {
          logger.error('FFmpeg error during video creation', { error: err.message });
          // Clean up concat file on error
          fs.unlink(concatFile).catch(() => {});
          reject(new Error(`Video creation failed: ${err.message}`));
        });

      // Set timeout for video creation (10 minutes)
      const timeout = setTimeout(() => {
        command.kill('SIGKILL');
        reject(new Error('Video creation timed out'));
      }, 10 * 60 * 1000);

      command.on('end', () => clearTimeout(timeout));
      command.on('error', () => clearTimeout(timeout));

      command.run();
    } catch (err) {
      logger.error('Error creating concat file', { error: err.message });
      reject(new Error(`Failed to create concat file: ${err.message}`));
    }
  });
};

// Health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const uploadDir = path.join(__dirname, config.uploadDir);
    const outputDir = path.join(__dirname, config.outputDir);
    const resourceCheck = await systemMonitor.checkSystemResources(uploadDir, outputDir);

    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      activeJobs,
      maxConcurrentJobs: config.maxConcurrentJobs,
      memory: resourceCheck.memory,
      diskSpace: resourceCheck.diskSpace,
      warnings: resourceCheck.warnings,
      canAcceptJobs: activeJobs < config.maxConcurrentJobs && resourceCheck.canProcess
    });
  } catch (error) {
    logger.error('Health check error', { error: error.message });
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// Routes
app.post('/api/upload', upload.single('video'), async (req, res) => {
  try {
    if (!req.file) {
      logger.warn('Upload attempt without file', { ip: req.ip });
      return res.status(400).json({ error: 'No video file uploaded' });
    }

    logger.info('File uploaded successfully', {
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      ip: req.ip
    });

    const metadata = await getVideoMetadata(req.file.path);

    // Validate video metadata
    if (!metadata.streams || metadata.streams.length === 0) {
      // Clean up invalid file
      await fs.unlink(req.file.path).catch(() => {});
      logger.warn('Invalid video file uploaded', { filename: req.file.filename });
      return res.status(400).json({ error: 'Invalid video file' });
    }

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    if (!videoStream) {
      // Clean up invalid file
      await fs.unlink(req.file.path).catch(() => {});
      logger.warn('No video stream found in file', { filename: req.file.filename });
      return res.status(400).json({ error: 'No video stream found in file' });
    }

    res.json({
      success: true,
      file: {
        id: path.parse(req.file.filename).name,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        duration: metadata.format.duration,
        width: videoStream.width,
        height: videoStream.height
      }
    });
  } catch (error) {
    logger.error('Upload error', { error: error.message, ip: req.ip });

    // Clean up file if it exists
    if (req.file) {
      await fs.unlink(req.file.path).catch(() => {});
    }

    res.status(500).json({ error: 'Failed to process uploaded file' });
  }
});

app.post('/api/process', async (req, res) => {
  try {
    // Check if we've reached max concurrent jobs
    if (activeJobs >= config.maxConcurrentJobs) {
      logger.warn('Max concurrent jobs reached', { activeJobs, maxJobs: config.maxConcurrentJobs });
      return res.status(429).json({ error: 'Server is busy. Please try again later.' });
    }

    // Validate input
    const { error, value } = uploadSchema.validate({
      interval: req.body.interval,
      fps: req.body.fps,
      filter: req.body.filter,
      socketId: req.body.socketId
    });

    if (error) {
      logger.warn('Invalid process parameters', { error: error.details });
      return res.status(400).json({ error: error.details[0].message });
    }

    const { fileId } = req.body;
    if (!fileId || !isValidFilename(fileId)) {
      logger.warn('Invalid file ID', { fileId });
      return res.status(400).json({ error: 'Invalid file ID provided' });
    }

    const { interval: frameInterval, fps: outputFps, filter: selectedFilter, socketId } = value;

    // Check system resources before processing
    const uploadDir = path.join(__dirname, config.uploadDir);
    const outputDir = path.join(__dirname, config.outputDir);
    const resourceCheck = await systemMonitor.checkSystemResources(uploadDir, outputDir);

    if (!resourceCheck.canProcess) {
      logger.warn('Insufficient system resources for processing', resourceCheck);
      return res.status(503).json({
        error: 'Server resources are currently insufficient for processing. Please try again later.',
        details: resourceCheck.warnings
      });
    }

    if (resourceCheck.warnings.length > 0) {
      logger.warn('System resource warnings', resourceCheck.warnings);
    }

    activeJobs++;
    logger.info('Starting video processing', {
      fileId,
      frameInterval,
      outputFps,
      selectedFilter,
      activeJobs,
      systemResources: {
        memoryUsed: `${resourceCheck.memory.process.rss}MB`,
        systemFree: `${resourceCheck.memory.system.free}MB`
      }
    });
    
    // Find the uploaded file
    let videoPath = null;
    const extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];

    for (const ext of extensions) {
      const testPath = path.join(__dirname, config.uploadDir, `${fileId}${ext}`);
      try {
        await fs.access(testPath);
        videoPath = testPath;
        logger.info('Found video file', { videoPath });
        break;
      } catch {
        // Continue checking other extensions
      }
    }

    if (!videoPath) {
      activeJobs--;
      logger.error('Video file not found', { fileId });
      return res.status(404).json({ error: 'Video file not found' });
    }
    
    const framesDir = path.join(__dirname, config.uploadDir, `${fileId}_frames`);
    const outputFilename = `${fileId}_stopmotion.mp4`;
    const outputPath = path.join(__dirname, config.outputDir, outputFilename);

    try {
      // Ensure output directory exists
      await fs.mkdir(path.join(__dirname, config.outputDir), { recursive: true });

      // Extract frames
      logger.info('Starting frame extraction', { fileId });
      const frames = await extractFrames(videoPath, framesDir, frameInterval, socketId);

      if (frames.length === 0) {
        throw new Error('No frames extracted from video');
      }

      logger.info('Frame extraction completed', { fileId, frameCount: frames.length });

      // Create stop motion video
      logger.info('Creating stop motion video', { fileId });
      await createStopMotion(frames, outputPath, outputFps, selectedFilter, socketId);

      // Clean up frames directory
      try {
        await fs.rm(framesDir, { recursive: true, force: true });
        logger.info('Cleaned up temporary frames', { fileId });
      } catch (err) {
        logger.error('Error cleaning up frames', { fileId, error: err });
      }

      // Send completion
      if (socketId) {
        io.to(socketId).emit('progress', {
          stage: 'complete',
          percent: 100
        });
      }

      logger.info('Video processing completed successfully', {
        fileId,
        outputFilename,
        frameCount: frames.length
      });

      res.json({
        success: true,
        output: {
          filename: outputFilename,
          url: `/output/${outputFilename}`,
          frameCount: frames.length
        }
      });

    } catch (error) {
      logger.error('Processing error', { fileId, error: error.message });

      // Send error to client via socket
      if (socketId) {
        io.to(socketId).emit('error', {
          message: error.message
        });
      }

      res.status(500).json({ error: error.message });
    } finally {
      activeJobs--;
      logger.info('Job completed, active jobs decreased', { activeJobs });
    }

  } catch (error) {
    activeJobs--;
    logger.error('Unexpected processing error', { error: error.message });
    res.status(500).json({ error: 'An unexpected error occurred during processing' });
  }
});

app.get('/api/download/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;

    // Validate filename for security (prevent path traversal)
    if (!isValidFilename(filename)) {
      logger.warn('Invalid filename attempted', { filename, ip: req.ip });
      return res.status(400).json({ error: 'Invalid filename' });
    }

    // Ensure file has valid video extension
    const allowedExtensions = /\.(mp4|webm|avi|mov|mkv)$/i;
    if (!allowedExtensions.test(filename)) {
      logger.warn('Invalid file extension attempted', { filename, ip: req.ip });
      return res.status(400).json({ error: 'Invalid file type' });
    }

    // Construct safe file path
    const filepath = path.join(__dirname, config.outputDir, path.basename(filename));

    // Verify file exists and is within output directory
    const resolvedPath = path.resolve(filepath);
    const outputDirPath = path.resolve(__dirname, config.outputDir);

    if (!resolvedPath.startsWith(outputDirPath)) {
      logger.warn('Path traversal attempt detected', { filename, ip: req.ip });
      return res.status(403).json({ error: 'Access denied' });
    }

    await fs.access(filepath);

    logger.info('File download', { filename, ip: req.ip });
    res.download(filepath, (err) => {
      if (err) {
        logger.error('Download error', { filename, error: err });
      }
    });
  } catch (error) {
    logger.error('Download endpoint error', { filename: req.params.filename, error });
    res.status(404).json({ error: 'File not found' });
  }
});

// Cleanup old files periodically
const cleanupOldFiles = async () => {
  try {
    const uploadsDir = path.join(__dirname, config.uploadDir);
    const outputDir = path.join(__dirname, config.outputDir);
    const maxAge = config.fileMaxAgeHours * 60 * 60 * 1000;

    const cleanOldFiles = async (dir, dirName) => {
      try {
        const files = await fs.readdir(dir);
        let cleanedCount = 0;

        for (const file of files) {
          const filepath = path.join(dir, file);
          try {
            const stat = await fs.stat(filepath);
            if (Date.now() - stat.mtime > maxAge) {
              await fs.unlink(filepath);
              cleanedCount++;
            }
          } catch (fileError) {
            logger.warn('Error processing file during cleanup', { file, error: fileError.message });
          }
        }

        if (cleanedCount > 0) {
          logger.info('Cleaned old files', { directory: dirName, count: cleanedCount });
        }
      } catch (dirError) {
        logger.error('Error accessing directory during cleanup', { directory: dirName, error: dirError.message });
      }
    };

    await cleanOldFiles(uploadsDir, 'uploads');
    await cleanOldFiles(outputDir, 'output');
  } catch (error) {
    logger.error('Cleanup error', { error: error.message });
  }
};

// Run cleanup immediately and then periodically
cleanupOldFiles();
setInterval(cleanupOldFiles, config.cleanupIntervalMinutes * 60 * 1000);

// Startup checks and server launch
const startServer = async () => {
  try {
    // Check FFmpeg availability
    const ffmpegAvailable = await checkFFmpegAvailability();
    if (!ffmpegAvailable) {
      logger.error('FFmpeg is not available. The application will not function properly.');
      if (config.nodeEnv === 'production') {
        process.exit(1);
      }
    }

    // Create necessary directories
    await fs.mkdir(path.join(__dirname, config.uploadDir), { recursive: true });
    await fs.mkdir(path.join(__dirname, config.outputDir), { recursive: true });

    // Start system monitoring
    systemMonitor.startMonitoring(5); // Monitor every 5 minutes

    // Start server
    server.listen(config.port, () => {
      logger.info('Server started successfully', {
        port: config.port,
        nodeEnv: config.nodeEnv,
        frontendUrl: config.frontendUrl,
        maxFileSize: `${config.maxFileSize / (1024 * 1024)}MB`,
        maxConcurrentJobs: config.maxConcurrentJobs
      });

      // Log initial system status
      systemMonitor.logSystemStatus();
    });

  } catch (error) {
    logger.error('Failed to start server', { error: error.message });
    process.exit(1);
  }
};

// Handle graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);

  // Stop system monitoring
  systemMonitor.stopMonitoring();

  // Close server
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });

  // Force exit after 10 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

startServer();
