.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 12px;
  margin: 2rem;
}

.error-boundary-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.error-boundary-content h2 {
  color: #e74c3c;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-boundary-content p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.error-details {
  text-align: left;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.error-details summary {
  cursor: pointer;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.error-details pre {
  background: #343a40;
  color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.8rem;
  margin: 0.5rem 0;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button,
.reload-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.retry-button {
  background: #28a745;
  color: white;
}

.retry-button:hover {
  background: #218838;
  transform: translateY(-1px);
}

.reload-button {
  background: #6c757d;
  color: white;
}

.reload-button:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .error-boundary {
    margin: 1rem;
    padding: 1rem;
  }
  
  .error-boundary-content {
    padding: 1.5rem;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .retry-button,
  .reload-button {
    width: 100%;
  }
}
