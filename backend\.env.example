# Server Configuration
PORT=5000
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=104857600
UPLOAD_DIR=uploads
OUTPUT_DIR=output

# Processing Configuration
MAX_CONCURRENT_JOBS=3
CLEANUP_INTERVAL_MINUTES=30
FILE_MAX_AGE_HOURS=1

# Rate Limiting (requests per 15 minutes)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# FFmpeg Configuration
FFMPEG_PATH=ffmpeg
