@echo off
echo ===================================
echo Stop Motion Creator - Setup Script
echo ===================================
echo.

echo Checking for Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo Node.js found!
echo.

echo Checking for FFmpeg...
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: FFmpeg is not installed or not in PATH!
    echo Please install FFmpeg from https://ffmpeg.org/download.html
    echo The app will not work without FFmpeg.
    echo.
    pause
)
echo FFmpeg found!
echo.

echo Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies!
    pause
    exit /b 1
)
echo Backend dependencies installed!
echo.

echo Installing frontend dependencies...
cd ../frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies!
    pause
    exit /b 1
)
echo Frontend dependencies installed!
echo.

echo Setting up environment files...
cd ..
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo Created backend/.env from example
)
if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env"
    echo Created frontend/.env from example
)
echo Environment setup complete!
echo.

cd ..
echo ===================================
echo Setup completed successfully!
echo ===================================
echo.
echo To run the application:
echo 1. Run start-backend.bat
echo 2. Run start-frontend.bat
echo.
pause
