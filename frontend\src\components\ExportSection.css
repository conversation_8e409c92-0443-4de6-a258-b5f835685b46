.export-section {
  margin-top: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-radius: 12px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  animation: slideUp 0.5s ease;
}

.export-header {
  text-align: center;
  margin-bottom: 2rem;
}

.export-header h3 {
  color: #333;
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
}

.export-header p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.format-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.format-selector label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.format-buttons {
  display: flex;
  gap: 0.5rem;
}

.format-btn {
  flex: 1;
  padding: 0.5rem 1rem;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.format-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.format-btn.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.export-actions {
  display: flex;
  gap: 1rem;
}

.download-btn,
.share-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.download-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.share-btn {
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
}

.share-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.btn-icon {
  font-size: 1.2rem;
}

.share-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  animation: fadeIn 0.3s ease;
}

.social-btn {
  padding: 10px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.social-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-btn.instagram:hover {
  border-color: #E4405F;
  color: #E4405F;
  background: rgba(228, 64, 95, 0.1);
}

.social-btn.tiktok:hover {
  border-color: #000;
  color: #000;
  background: rgba(0, 0, 0, 0.05);
}

.social-btn.twitter:hover {
  border-color: #1DA1F2;
  color: #1DA1F2;
  background: rgba(29, 161, 242, 0.1);
}

.social-btn.facebook:hover {
  border-color: #1877F2;
  color: #1877F2;
  background: rgba(24, 119, 242, 0.1);
}

.export-info {
  padding-top: 1.5rem;
  border-top: 1px solid rgba(102, 126, 234, 0.2);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.info-label {
  font-weight: 600;
  color: #555;
  min-width: 80px;
}

.info-value {
  color: #666;
  word-break: break-all;
}

.info-value a {
  color: #667eea;
  text-decoration: none;
  transition: color 0.3s ease;
}

.info-value a:hover {
  color: #764ba2;
  text-decoration: underline;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .export-actions {
    flex-direction: column;
  }
  
  .share-options {
    grid-template-columns: repeat(2, 1fr);
  }
}
