const fs = require('fs').promises;
const path = require('path');

class SystemMonitor {
  constructor(logger) {
    this.logger = logger;
  }

  // Check available disk space
  async checkDiskSpace(directory) {
    try {
      const stats = await fs.stat(directory);
      
      // For a more accurate disk space check, we'd need a platform-specific solution
      // For now, we'll do a basic check by trying to write a small test file
      const testFile = path.join(directory, '.disk_space_test');
      
      try {
        await fs.writeFile(testFile, 'test');
        await fs.unlink(testFile);
        return { available: true, message: 'Disk space available' };
      } catch (error) {
        if (error.code === 'ENOSPC') {
          return { available: false, message: 'Insufficient disk space' };
        }
        throw error;
      }
    } catch (error) {
      this.logger.error('Error checking disk space', { directory, error: error.message });
      return { available: false, message: 'Unable to check disk space' };
    }
  }

  // Get memory usage information
  getMemoryUsage() {
    const usage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();
    
    return {
      process: {
        rss: Math.round(usage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024), // MB
        external: Math.round(usage.external / 1024 / 1024) // MB
      },
      system: {
        total: Math.round(totalMemory / 1024 / 1024), // MB
        free: Math.round(freeMemory / 1024 / 1024), // MB
        used: Math.round((totalMemory - freeMemory) / 1024 / 1024) // MB
      }
    };
  }

  // Check if system has enough resources for processing
  async checkSystemResources(uploadDir, outputDir) {
    const results = {
      diskSpace: { upload: null, output: null },
      memory: null,
      canProcess: true,
      warnings: []
    };

    // Check disk space
    results.diskSpace.upload = await this.checkDiskSpace(uploadDir);
    results.diskSpace.output = await this.checkDiskSpace(outputDir);

    if (!results.diskSpace.upload.available || !results.diskSpace.output.available) {
      results.canProcess = false;
      results.warnings.push('Insufficient disk space');
    }

    // Check memory
    results.memory = this.getMemoryUsage();
    
    // Warn if process is using more than 80% of available memory
    const memoryUsagePercent = (results.memory.process.rss / results.memory.system.total) * 100;
    if (memoryUsagePercent > 80) {
      results.warnings.push(`High memory usage: ${memoryUsagePercent.toFixed(1)}%`);
    }

    // Warn if system has less than 500MB free memory
    if (results.memory.system.free < 500) {
      results.warnings.push(`Low system memory: ${results.memory.system.free}MB free`);
    }

    return results;
  }

  // Log system status
  logSystemStatus() {
    const memory = this.getMemoryUsage();
    this.logger.info('System status', {
      memory: {
        processRSS: `${memory.process.rss}MB`,
        heapUsed: `${memory.process.heapUsed}MB`,
        systemFree: `${memory.system.free}MB`
      },
      uptime: `${Math.round(process.uptime())}s`
    });
  }

  // Start periodic monitoring
  startMonitoring(intervalMinutes = 5) {
    this.logger.info('Starting system monitoring', { intervalMinutes });
    
    // Log initial status
    this.logSystemStatus();
    
    // Set up periodic monitoring
    this.monitoringInterval = setInterval(() => {
      this.logSystemStatus();
      
      // Force garbage collection if available (for development)
      if (global.gc && process.env.NODE_ENV === 'development') {
        global.gc();
        this.logger.info('Forced garbage collection');
      }
    }, intervalMinutes * 60 * 1000);
  }

  // Stop monitoring
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.logger.info('Stopped system monitoring');
    }
  }
}

module.exports = SystemMonitor;
