.settings-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.settings-title {
  font-size: 1.3rem;
  color: #333;
  margin: 0;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: help;
  opacity: 0.6;
  font-size: 0.8rem;
}

.tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
  font-weight: normal;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.slider-container {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e0e0e0;
  outline: none;
  -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  transition: transform 0.2s;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: none;
}

.number-input {
  width: 70px;
  padding: 6px 10px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
  transition: border-color 0.3s;
}

.number-input:focus {
  outline: none;
  border-color: #667eea;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.filter-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-option:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.filter-option.active {
  border-color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.filter-icon {
  font-size: 1.2rem;
}

.filter-label {
  font-size: 0.8rem;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.process-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.process-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.process-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.reset-button {
  background: #f0f0f0;
  color: #666;
  border: none;
  padding: 10px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-button:hover:not(:disabled) {
  background: #e0e0e0;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.info-box {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
}

.info-box h4 {
  margin: 0 0 0.5rem 0;
  color: #667eea;
  font-size: 0.9rem;
}

.info-box ul {
  margin: 0;
  padding-left: 1.2rem;
  list-style: none;
}

.info-box li {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.5;
  position: relative;
  padding-left: 1rem;
}

.info-box li:before {
  content: "→";
  position: absolute;
  left: 0;
  color: #764ba2;
}
