# Dependencies
node_modules/
package-lock.json
yarn.lock

# Backend
backend/uploads/
backend/output/
backend/*.log

# Frontend build
frontend/build/
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Testing
coverage/
*.lcov
.nyc_output

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Temporary files
*.tmp
*.temp
temp/
tmp/
