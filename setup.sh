#!/bin/bash

echo "==================================="
echo "Stop Motion Creator - Setup Script"
echo "==================================="
echo ""

echo "Checking for Node.js..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi
echo "Node.js found: $(node --version)"
echo ""

echo "Checking for FFmpeg..."
if ! command -v ffmpeg &> /dev/null; then
    echo "WARNING: FFmpeg is not installed!"
    echo "Please install FFmpeg:"
    echo "  macOS: brew install ffmpeg"
    echo "  Linux: sudo apt install ffmpeg"
    echo "The app will not work without FFmpeg."
    echo ""
fi
echo "FFmpeg found!"
echo ""

echo "Installing backend dependencies..."
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install backend dependencies!"
    exit 1
fi
echo "Backend dependencies installed!"
echo ""

echo "Installing frontend dependencies..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install frontend dependencies!"
    exit 1
fi
echo "Frontend dependencies installed!"
echo ""

echo "Setting up environment files..."
cd ..
if [ ! -f "backend/.env" ]; then
    cp "backend/.env.example" "backend/.env"
    echo "Created backend/.env from example"
fi
if [ ! -f "frontend/.env" ]; then
    cp "frontend/.env.example" "frontend/.env"
    echo "Created frontend/.env from example"
fi
echo "Environment setup complete!"
echo ""

cd ..
echo "==================================="
echo "Setup completed successfully!"
echo "==================================="
echo ""
echo "To run the application:"
echo "1. Run ./start-backend.sh"
echo "2. Run ./start-frontend.sh"
echo ""
