import React from 'react';
import './ProgressBar.css';

function ProgressBar({ stage, percent }) {
  const getStageMessage = () => {
    switch(stage) {
      case 'starting':
        return 'Initializing...';
      case 'extracting':
        return 'Extracting frames...';
      case 'creating':
        return 'Creating stop motion...';
      case 'complete':
        return 'Complete!';
      default:
        return 'Processing...';
    }
  };

  const getStageIcon = () => {
    switch(stage) {
      case 'starting':
        return '🚀';
      case 'extracting':
        return '🎞️';
      case 'creating':
        return '🎬';
      case 'complete':
        return '✅';
      default:
        return '⏳';
    }
  };

  return (
    <div className="progress-bar-container">
      <div className="progress-header">
        <span className="progress-icon">{getStageIcon()}</span>
        <span className="progress-message">{getStageMessage()}</span>
        <span className="progress-percent">{Math.round(percent)}%</span>
      </div>
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${percent}%` }}
        >
          <div className="progress-shine"></div>
        </div>
      </div>
    </div>
  );
}

export default ProgressBar;
