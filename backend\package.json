{"name": "stop-motion-backend", "version": "1.0.0", "description": "Backend for stop-motion video processing app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.19.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "fluent-ffmpeg": "^2.1.2", "uuid": "^9.0.1", "socket.io": "^4.7.2", "joi": "^17.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}