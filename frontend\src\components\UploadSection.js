import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { useDropzone } from 'react-dropzone';
import './UploadSection.css';

function UploadSection({ onUpload }) {
  const maxFileSize = parseInt(process.env.REACT_APP_MAX_FILE_SIZE) || 104857600; // 100MB
  const supportedFormats = process.env.REACT_APP_SUPPORTED_FORMATS?.split(',') || ['mp4', 'avi', 'mov', 'mkv', 'webm'];

  const onDrop = useCallback((acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      onUpload(acceptedFiles[0]);
    }
  }, [onUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'video/*': supportedFormats.map(format => `.${format}`)
    },
    maxFiles: 1,
    maxSize: maxFileSize
  });

  return (
    <div className="upload-section">
      <div 
        {...getRootProps()} 
        className={`upload-area ${isDragActive ? 'active' : ''}`}
      >
        <input {...getInputProps()} />
        <div className="upload-content">
          <div className="upload-icon">📹</div>
          <h2>Upload Your Video</h2>
          {isDragActive ? (
            <p>Drop the video here...</p>
          ) : (
            <>
              <p>Drag & drop your video here, or click to select</p>
              <p className="upload-hint">
                Supports {supportedFormats.map(f => f.toUpperCase()).join(', ')}
                (Max {Math.round(maxFileSize / (1024 * 1024))}MB)
              </p>
            </>
          )}
          <button className="upload-button">Choose Video</button>
        </div>
      </div>

      <div className="features-grid">
        <div className="feature-card">
          <span className="feature-icon">⚡</span>
          <h3>Fast Processing</h3>
          <p>Quick frame extraction and rendering</p>
        </div>
        <div className="feature-card">
          <span className="feature-icon">🎨</span>
          <h3>Visual Effects</h3>
          <p>Apply cartoon, vibrant, clay, and more filters</p>
        </div>
        <div className="feature-card">
          <span className="feature-icon">⚙️</span>
          <h3>Custom Settings</h3>
          <p>Control frame interval and playback speed</p>
        </div>
      </div>
    </div>
  );
}

UploadSection.propTypes = {
  onUpload: PropTypes.func.isRequired
};

export default UploadSection;
