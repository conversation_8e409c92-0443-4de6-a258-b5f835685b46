import React, { useState } from 'react';
import PropTypes from 'prop-types';
import './VideoPreview.css';

function VideoPreview({ originalVideo, outputVideo, apiUrl }) {
  const [activeView, setActiveView] = useState('original');

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="video-preview">
      <div className="preview-tabs">
        <button 
          className={`tab ${activeView === 'original' ? 'active' : ''}`}
          onClick={() => setActiveView('original')}
        >
          📹 Original
        </button>
        {outputVideo && (
          <button 
            className={`tab ${activeView === 'output' ? 'active' : ''}`}
            onClick={() => setActiveView('output')}
          >
            🎬 Stop Motion
          </button>
        )}
        {outputVideo && (
          <button 
            className={`tab ${activeView === 'compare' ? 'active' : ''}`}
            onClick={() => setActiveView('compare')}
          >
            🔄 Compare
          </button>
        )}
      </div>

      <div className="preview-content">
        {activeView === 'original' && originalVideo && (
          <div className="video-container">
            <div className="video-info">
              <h3>Original Video</h3>
              <div className="info-grid">
                <span>📁 {originalVideo.originalName}</span>
                <span>💾 {formatFileSize(originalVideo.size)}</span>
                <span>⏱️ {formatDuration(originalVideo.duration)}</span>
                <span>📐 {originalVideo.width}x{originalVideo.height}</span>
              </div>
            </div>
            <div className="video-placeholder">
              <div className="placeholder-icon">📹</div>
              <p>Original video uploaded</p>
              <p className="video-name">{originalVideo.originalName}</p>
            </div>
          </div>
        )}

        {activeView === 'output' && outputVideo && (
          <div className="video-container">
            <div className="video-info">
              <h3>Stop Motion Output</h3>
              <div className="info-grid">
                <span>🎞️ {outputVideo.frameCount} frames</span>
                <span>📁 {outputVideo.filename}</span>
              </div>
            </div>
            <video 
              controls 
              className="video-player"
              src={`${apiUrl}${outputVideo.url}`}
            >
              Your browser does not support the video tag.
            </video>
          </div>
        )}

        {activeView === 'compare' && outputVideo && (
          <div className="compare-container">
            <div className="compare-video">
              <h4>Original</h4>
              <div className="video-placeholder small">
                <div className="placeholder-icon">📹</div>
              </div>
            </div>
            <div className="compare-arrow">→</div>
            <div className="compare-video">
              <h4>Stop Motion</h4>
              <video 
                controls 
                className="video-player small"
                src={`${apiUrl}${outputVideo.url}`}
              >
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        )}

        {!originalVideo && !outputVideo && (
          <div className="no-video">
            <p>No video loaded</p>
          </div>
        )}
      </div>
    </div>
  );
}

VideoPreview.propTypes = {
  originalVideo: PropTypes.shape({
    id: PropTypes.string,
    filename: PropTypes.string,
    originalName: PropTypes.string.isRequired,
    size: PropTypes.number.isRequired,
    duration: PropTypes.number.isRequired,
    width: PropTypes.number.isRequired,
    height: PropTypes.number.isRequired
  }),
  outputVideo: PropTypes.shape({
    filename: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
    frameCount: PropTypes.number.isRequired
  }),
  apiUrl: PropTypes.string.isRequired
};

export default VideoPreview;
