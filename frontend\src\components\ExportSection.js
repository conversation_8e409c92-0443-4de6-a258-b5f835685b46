import React, { useState } from 'react';
import PropTypes from 'prop-types';
import './ExportSection.css';

function ExportSection({ outputVideo, apiUrl }) {
  const [exportFormat, setExportFormat] = useState('mp4');
  const [showShareOptions, setShowShareOptions] = useState(false);

  const handleDownload = () => {
    const downloadUrl = `${apiUrl}/api/download/${outputVideo.filename}`;
    window.open(downloadUrl, '_blank');
  };

  const handleShare = (platform) => {
    // In a real app, these would integrate with social media APIs
    const shareMessages = {
      instagram: 'Check out my stop motion video!',
      tiktok: 'Created an amazing stop motion effect!',
      twitter: 'Just made this cool stop motion video! 🎬',
      facebook: 'Look at this stop motion transformation!'
    };
    
    alert(`Share to ${platform}: ${shareMessages[platform]}\n\n(Social sharing would be implemented with proper APIs)`);
  };

  return (
    <div className="export-section">
      <div className="export-header">
        <h3>🎉 Your Stop Motion is Ready!</h3>
        <p>{outputVideo.frameCount} frames processed successfully</p>
      </div>

      <div className="export-options">
        <div className="format-selector">
          <label>Export Format:</label>
          <div className="format-buttons">
            <button 
              className={`format-btn ${exportFormat === 'mp4' ? 'active' : ''}`}
              onClick={() => setExportFormat('mp4')}
            >
              MP4
            </button>
            <button 
              className={`format-btn ${exportFormat === 'gif' ? 'active' : ''}`}
              onClick={() => setExportFormat('gif')}
            >
              GIF
            </button>
            <button 
              className={`format-btn ${exportFormat === 'webm' ? 'active' : ''}`}
              onClick={() => setExportFormat('webm')}
            >
              WEBM
            </button>
          </div>
        </div>

        <div className="export-actions">
          <button className="download-btn" onClick={handleDownload}>
            <span className="btn-icon">⬇️</span>
            Download {exportFormat.toUpperCase()}
          </button>
          
          <button 
            className="share-btn"
            onClick={() => setShowShareOptions(!showShareOptions)}
          >
            <span className="btn-icon">🔗</span>
            Share
          </button>
        </div>

        {showShareOptions && (
          <div className="share-options">
            <button 
              className="social-btn instagram"
              onClick={() => handleShare('instagram')}
            >
              📷 Instagram
            </button>
            <button 
              className="social-btn tiktok"
              onClick={() => handleShare('tiktok')}
            >
              🎵 TikTok
            </button>
            <button 
              className="social-btn twitter"
              onClick={() => handleShare('twitter')}
            >
              🐦 Twitter
            </button>
            <button 
              className="social-btn facebook"
              onClick={() => handleShare('facebook')}
            >
              📘 Facebook
            </button>
          </div>
        )}
      </div>

      <div className="export-info">
        <div className="info-item">
          <span className="info-label">📁 File:</span>
          <span className="info-value">{outputVideo.filename}</span>
        </div>
        <div className="info-item">
          <span className="info-label">🎞️ Frames:</span>
          <span className="info-value">{outputVideo.frameCount}</span>
        </div>
        <div className="info-item">
          <span className="info-label">🔗 URL:</span>
          <span className="info-value">
            <a href={`${apiUrl}${outputVideo.url}`} target="_blank" rel="noopener noreferrer">
              View Output
            </a>
          </span>
        </div>
      </div>
    </div>
  );
}

ExportSection.propTypes = {
  outputVideo: PropTypes.shape({
    filename: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
    frameCount: PropTypes.number.isRequired
  }).isRequired,
  apiUrl: PropTypes.string.isRequired
};

export default ExportSection;
