import React, { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';
import './App.css';
import ErrorBoundary from './components/ErrorBoundary';
import UploadSection from './components/UploadSection';
import SettingsPanel from './components/SettingsPanel';
import VideoPreview from './components/VideoPreview';
import ProgressBar from './components/ProgressBar';
import ExportSection from './components/ExportSection';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

function App() {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState({ stage: '', percent: 0 });
  const [outputVideo, setOutputVideo] = useState(null);
  const [socket, setSocket] = useState(null);
  const [settings, setSettings] = useState({
    interval: 0.3,
    fps: 10,
    filter: 'none'
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    const newSocket = io(API_URL, {
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    setSocket(newSocket);

    newSocket.on('progress', (data) => {
      setProgress(data);
    });

    newSocket.on('error', (error) => {
      console.error('Socket error:', error);
      setError('Connection error. Please refresh the page.');
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setError('Unable to connect to server. Please check your connection.');
    });

    newSocket.on('disconnect', (reason) => {
      console.warn('Socket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        newSocket.connect();
      }
    });

    return () => {
      newSocket.close();
    };
  }, []);

  const handleFileUpload = async (file) => {
    const formData = new FormData();
    formData.append('video', file);

    try {
      setError(null);
      const response = await axios.post(`${API_URL}/api/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 30000 // 30 second timeout
      });

      if (response.data.success) {
        setUploadedFile(response.data.file);
        setOutputVideo(null);
      }
    } catch (error) {
      console.error('Upload error:', error);

      let errorMessage = 'Failed to upload video. Please try again.';
      if (error.response) {
        // Server responded with error status
        errorMessage = error.response.data.error || errorMessage;
      } else if (error.request) {
        // Request was made but no response received
        errorMessage = 'Network error. Please check your connection.';
      } else if (error.code === 'ECONNABORTED') {
        // Request timeout
        errorMessage = 'Upload timed out. Please try with a smaller file.';
      }

      setError(errorMessage);
    }
  };

  const handleProcess = async () => {
    if (!uploadedFile || !socket) {
      setError('Please upload a video file first.');
      return;
    }

    setProcessing(true);
    setError(null);
    setProgress({ stage: 'starting', percent: 0 });

    try {
      const response = await axios.post(`${API_URL}/api/process`, {
        fileId: uploadedFile.id,
        interval: settings.interval,
        fps: settings.fps,
        filter: settings.filter,
        socketId: socket.id
      }, {
        timeout: 600000 // 10 minute timeout for processing
      });

      if (response.data.success) {
        setOutputVideo(response.data.output);
      }
    } catch (error) {
      console.error('Processing error:', error);

      let errorMessage = 'Failed to process video. Please try again.';
      if (error.response) {
        errorMessage = error.response.data.error || errorMessage;
        if (error.response.status === 429) {
          errorMessage = 'Server is busy. Please try again in a few minutes.';
        }
      } else if (error.request) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Processing timed out. Please try with a shorter video.';
      }

      setError(errorMessage);
    } finally {
      setProcessing(false);
      setProgress({ stage: '', percent: 0 });
    }
  };

  const handleReset = () => {
    setUploadedFile(null);
    setOutputVideo(null);
    setProgress({ stage: '', percent: 0 });
    setError(null);
  };

  return (
    <ErrorBoundary>
      <div className="App">
        <header className="app-header">
          <h1>🎬 Stop Motion Creator</h1>
          <p>Transform your videos into stunning stop-motion animations</p>
        </header>

        <main className="app-main">
          {error && (
            <div className="error-message">
              <span>{error}</span>
              <button onClick={() => setError(null)}>×</button>
            </div>
          )}

          {!uploadedFile ? (
            <ErrorBoundary>
              <UploadSection onUpload={handleFileUpload} />
            </ErrorBoundary>
          ) : (
            <div className="editor-container">
              <div className="editor-main">
                <ErrorBoundary>
                  <VideoPreview
                    originalVideo={uploadedFile}
                    outputVideo={outputVideo}
                    apiUrl={API_URL}
                  />
                </ErrorBoundary>

                {processing && (
                  <ErrorBoundary>
                    <ProgressBar
                      stage={progress.stage}
                      percent={progress.percent}
                    />
                  </ErrorBoundary>
                )}

                {outputVideo && (
                  <ErrorBoundary>
                    <ExportSection
                      outputVideo={outputVideo}
                      apiUrl={API_URL}
                    />
                  </ErrorBoundary>
                )}
              </div>

              <div className="editor-sidebar">
                <ErrorBoundary>
                  <SettingsPanel
                    settings={settings}
                    onChange={setSettings}
                    onProcess={handleProcess}
                    onReset={handleReset}
                    processing={processing}
                    hasOutput={!!outputVideo}
                  />
                </ErrorBoundary>
              </div>
            </div>
          )}
        </main>

        <footer className="app-footer">
          <p>© 2024 Stop Motion Creator | Made with ❤️</p>
        </footer>
      </div>
    </ErrorBoundary>
  );
}

export default App;
