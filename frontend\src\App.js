import React, { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';
import './App.css';
import UploadSection from './components/UploadSection';
import SettingsPanel from './components/SettingsPanel';
import VideoPreview from './components/VideoPreview';
import ProgressBar from './components/ProgressBar';
import ExportSection from './components/ExportSection';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

function App() {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState({ stage: '', percent: 0 });
  const [outputVideo, setOutputVideo] = useState(null);
  const [socket, setSocket] = useState(null);
  const [settings, setSettings] = useState({
    interval: 0.3,
    fps: 10,
    filter: 'none'
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    const newSocket = io(API_URL);
    setSocket(newSocket);

    newSocket.on('progress', (data) => {
      setProgress(data);
    });

    return () => newSocket.close();
  }, []);

  const handleFileUpload = async (file) => {
    const formData = new FormData();
    formData.append('video', file);

    try {
      setError(null);
      const response = await axios.post(`${API_URL}/api/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        setUploadedFile(response.data.file);
        setOutputVideo(null);
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload video. Please try again.');
    }
  };

  const handleProcess = async () => {
    if (!uploadedFile || !socket) return;

    setProcessing(true);
    setError(null);
    setProgress({ stage: 'starting', percent: 0 });

    try {
      const response = await axios.post(`${API_URL}/api/process`, {
        fileId: uploadedFile.id,
        interval: settings.interval,
        fps: settings.fps,
        filter: settings.filter,
        socketId: socket.id
      });

      if (response.data.success) {
        setOutputVideo(response.data.output);
      }
    } catch (error) {
      console.error('Processing error:', error);
      setError('Failed to process video. Please try again.');
    } finally {
      setProcessing(false);
      setProgress({ stage: '', percent: 0 });
    }
  };

  const handleReset = () => {
    setUploadedFile(null);
    setOutputVideo(null);
    setProgress({ stage: '', percent: 0 });
    setError(null);
  };

  return (
    <div className="App">
      <header className="app-header">
        <h1>🎬 Stop Motion Creator</h1>
        <p>Transform your videos into stunning stop-motion animations</p>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-message">
            <span>{error}</span>
            <button onClick={() => setError(null)}>×</button>
          </div>
        )}

        {!uploadedFile ? (
          <UploadSection onUpload={handleFileUpload} />
        ) : (
          <div className="editor-container">
            <div className="editor-main">
              <VideoPreview 
                originalVideo={uploadedFile}
                outputVideo={outputVideo}
                apiUrl={API_URL}
              />
              
              {processing && (
                <ProgressBar 
                  stage={progress.stage}
                  percent={progress.percent}
                />
              )}

              {outputVideo && (
                <ExportSection 
                  outputVideo={outputVideo}
                  apiUrl={API_URL}
                />
              )}
            </div>

            <div className="editor-sidebar">
              <SettingsPanel 
                settings={settings}
                onChange={setSettings}
                onProcess={handleProcess}
                onReset={handleReset}
                processing={processing}
                hasOutput={!!outputVideo}
              />
            </div>
          </div>
        )}
      </main>

      <footer className="app-footer">
        <p>© 2024 Stop Motion Creator | Made with ❤️</p>
      </footer>
    </div>
  );
}

export default App;
