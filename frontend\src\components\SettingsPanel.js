import React from 'react';
import PropTypes from 'prop-types';
import './SettingsPanel.css';

function SettingsPanel({ settings, onChange, onProcess, onReset, processing, hasOutput }) {
  const handleChange = (field, value) => {
    onChange({
      ...settings,
      [field]: value
    });
  };

  const filters = [
    { value: 'none', label: 'None', icon: '✨' },
    { value: 'cartoon', label: 'Cartoon', icon: '🎨' },
    { value: 'vibrant', label: 'Vibrant', icon: '🌈' },
    { value: 'clay', label: 'Clay', icon: '🏺' },
    { value: 'retro', label: 'Retro', icon: '📼' },
    { value: 'sketch', label: 'Sketch', icon: '✏️' }
  ];

  return (
    <div className="settings-panel">
      <h3 className="settings-title">⚙️ Settings</h3>
      
      <div className="setting-group">
        <label htmlFor="interval" className="setting-label">
          Frame Interval (seconds)
          <span className="tooltip">ℹ️
            <span className="tooltip-text">
              Extract one frame every X seconds<br/>
              • 0.1s = Very smooth (10 frames/sec)<br/>
              • 0.2s = Smooth (5 frames/sec)<br/>
              • 0.5s = Choppy (2 frames/sec)<br/>
              • 1.0s = Very choppy (1 frame/sec)
            </span>
          </span>
        </label>
        <div className="slider-container">
          <input
            type="range"
            id="interval"
            min="0.1"
            max="2"
            step="0.1"
            value={settings.interval}
            onChange={(e) => handleChange('interval', parseFloat(e.target.value))}
            className="slider"
          />
          <input
            type="number"
            min="0.1"
            max="2"
            step="0.1"
            value={settings.interval}
            onChange={(e) => handleChange('interval', parseFloat(e.target.value))}
            className="number-input"
          />
        </div>
      </div>

      <div className="setting-group">
        <label htmlFor="fps" className="setting-label">
          Output Speed (FPS)
          <span className="tooltip">ℹ️
            <span className="tooltip-text">
              Frames per second in the output video<br/>
              • 4-6 FPS = Slow, dramatic<br/>
              • 8-12 FPS = Classic stop motion<br/>
              • 15+ FPS = Fast, smooth
            </span>
          </span>
        </label>
        <div className="slider-container">
          <input
            type="range"
            id="fps"
            min="1"
            max="30"
            step="1"
            value={settings.fps}
            onChange={(e) => handleChange('fps', parseInt(e.target.value))}
            className="slider"
          />
          <input
            type="number"
            min="1"
            max="30"
            step="1"
            value={settings.fps}
            onChange={(e) => handleChange('fps', parseInt(e.target.value))}
            className="number-input"
          />
        </div>
      </div>

      <div className="setting-group">
        <label className="setting-label">
          Visual Filter
          <span className="tooltip">ℹ️
            <span className="tooltip-text">Apply artistic effects to your stop motion</span>
          </span>
        </label>
        <div className="filter-grid">
          {filters.map(filter => (
            <button
              key={filter.value}
              className={`filter-option ${settings.filter === filter.value ? 'active' : ''}`}
              onClick={() => handleChange('filter', filter.value)}
            >
              <span className="filter-icon">{filter.icon}</span>
              <span className="filter-label">{filter.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="action-buttons">
        <button 
          className="process-button"
          onClick={onProcess}
          disabled={processing}
        >
          {processing ? (
            <>
              <span className="spinner"></span>
              Processing...
            </>
          ) : (
            <>
              🎬 Create Stop Motion
            </>
          )}
        </button>
        
        {hasOutput && (
          <button 
            className="reset-button"
            onClick={onReset}
            disabled={processing}
          >
            🔄 Start New
          </button>
        )}
      </div>

      <div className="info-box">
        <h4>💡 Stop Motion Tips</h4>
        <ul>
          <li><strong>For smooth motion:</strong> Use 0.1-0.2s interval</li>
          <li><strong>For classic stop motion:</strong> Use 0.3-0.5s interval</li>
          <li><strong>Playback speed:</strong> 8-12 FPS works best</li>
          <li><strong>Filters:</strong> Clay and Cartoon work great for stop motion!</li>
          <li><strong>Video length:</strong> 20s video → ~2-4s stop motion</li>
        </ul>
      </div>
    </div>
  );
}

SettingsPanel.propTypes = {
  settings: PropTypes.shape({
    interval: PropTypes.number.isRequired,
    fps: PropTypes.number.isRequired,
    filter: PropTypes.string.isRequired
  }).isRequired,
  onChange: PropTypes.func.isRequired,
  onProcess: PropTypes.func.isRequired,
  onReset: PropTypes.func.isRequired,
  processing: PropTypes.bool.isRequired,
  hasOutput: PropTypes.bool.isRequired
};

export default SettingsPanel;
