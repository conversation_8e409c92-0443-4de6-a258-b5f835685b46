.video-preview {
  width: 100%;
}

.preview-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #f0f0f0;
}

.tab {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #999;
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
}

.tab:hover {
  color: #667eea;
}

.tab.active {
  color: #667eea;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.preview-content {
  min-height: 400px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
}

.video-container {
  width: 100%;
}

.video-info {
  margin-bottom: 1rem;
}

.video-info h3 {
  color: #333;
  font-size: 1.2rem;
  margin: 0 0 0.5rem 0;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.info-grid span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.video-placeholder {
  width: 100%;
  aspect-ratio: 16/9;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border: 2px dashed #d0d0d0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.video-placeholder.small {
  aspect-ratio: 16/9;
}

.placeholder-icon {
  font-size: 3rem;
  opacity: 0.5;
}

.video-placeholder p {
  margin: 0;
  color: #666;
}

.video-name {
  font-size: 0.9rem;
  color: #999;
  font-style: italic;
}

.video-player {
  width: 100%;
  border-radius: 8px;
  background: #000;
}

.video-player.small {
  width: 100%;
}

.compare-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
}

.compare-video {
  width: 100%;
}

.compare-video h4 {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.compare-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.no-video {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #999;
}

@media (max-width: 768px) {
  .preview-tabs {
    flex-wrap: wrap;
  }
  
  .tab {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
  
  .compare-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .compare-arrow {
    transform: rotate(90deg);
    text-align: center;
  }
}
